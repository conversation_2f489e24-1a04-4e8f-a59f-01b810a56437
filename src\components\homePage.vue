<template>
    <div class="">       
        <div class="bg-primary">
            <h1 class="text-2xl font-bold text-green-800 ml-16">OPTIMIZATION HUB</h1>
            <h3 class="text-sm text-green-800 ml-16 pb-16">Track, Run & Review Optimizers</h3>
        </div>
        <div class="bg-secondary h-[5px] w-full"></div>
        <div class="p-8">
            <div class="max-w-7xl mx-auto border bg-white mt-[-8%] rounded-lg p-4">
                <!-- Header -->
                <div class="flex items-center mb-3 ml-2">
                    <p class="text-gray-800 font-medium mr-4">Get started with Optimization</p>
                    <button
                        class="flex items-center gap-1 text-sm bg-tertiary text-white font-semibold border border-primary px-3 py-1.5 rounded hover:bg-tertiary hover:text-white transition" @click="createOptimizer">
                        + Start Optimization
                    </button>
                </div>

                <!-- Search and Filter Bar -->
                <div class="flex items-center bg-white p-1 rounded shadow mb-4 w-1/2">
                    <input type="text" v-model="search" placeholder="Search optimizers..."
                        class="flex-1 px-4 h-10 py-2 border rounded" @input="handleSearch" />
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-green-800"></div>
                    <p class="mt-2 text-gray-600">Loading optimizers...</p>
                </div>

                <!-- Optimizer Sections -->
                <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Completed Optimizers -->
                    <div class="border-1 p-3 rounded shadow-md">
                        <h2 class="text-lg font-semibold mb-2 flex">
                            <CheckCircle class="flex bg text-secondary mr-3" /> Completed Optimizers <span
                                class="bg-primary ml-3 text-green-800 text-sm font-medium px-2 py-0.5 rounded">{{
                                completedOptimizers.totalCount }}</span>
                        </h2>

                        <div v-if="completedOptimizers.data.length === 0" class="text-center py-8 text-gray-500">
                            No completed optimizers found
                        </div>

                        <div v-for="opt in completedOptimizers.data" :key="opt.id"
                            class="bg-white rounded p-3 shadow mb-2 transition-transform transform hover:shadow-lg hover:scale-[1.01] hover:bg-gray-50 cursor-pointer">

                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-semibold"> {{ opt.name }}</h3>
                            </div>
                            <div class="flex">
                                <div class="text-xs text-gray-500 mb-2">Event: {{ opt.event_name }} | Evaluation Period: {{ formatDate(opt.eval_start) }} - {{ formatDate(opt.eval_end) }}</div>
                            </div>
                            <div class="flex gap-2">
                                <button
                                    class="text-xs px-2 py-1 rounded bg-green-100 text-green-800 hover:bg-green-200 transition">Evaluation
                                    Summary</button>
                            </div>
                        </div>

                        <!-- Pagination for Completed -->
                        <Pagination 
                            :current-page="completedOptimizers.currentPage"
                            :total-pages="completedOptimizers.totalPages"
                            :total-items="completedOptimizers.totalCount"
                            :page-size="completedOptimizers.pageSize"
                            @page-changed="onCompletedPageChange"
                        />
                    </div>

                    <!-- Ongoing Optimizers -->
                    <div class="border-1 p-3 rounded shadow-md">
                        <h2 class="text-lg font-semibold mb-2 flex">
                            <Clock class="text-orange-500 mr-3" /> Ongoing Optimizers <span
                                class="ml-3 bg-orange-100 text-orange-700 text-sm font-medium px-2 py-0.5 rounded">{{
                                ongoingOptimizers.totalCount }}</span>
                        </h2>

                        <div v-if="ongoingOptimizers.data.length === 0" class="text-center py-8 text-gray-500">
                            No ongoing optimizers found
                        </div>

                        <div v-for="opt in ongoingOptimizers.data" :key="opt.id"
                            class="bg-white rounded p-3 shadow mb-2 transition-transform transform hover:shadow-lg hover:scale-[1.01] hover:bg-gray-50 cursor-pointer">

                            <div class="flex items-center justify-between">
                                <h3 class="flex font-semibold mb-2"> {{ opt.name }}</h3>
                            </div>
                            <div class="text-xs text-gray-500 mb-2">Event: {{ opt.event_name }} | Evaluation Period: {{ formatDate(opt.eval_start) }} - {{ formatDate(opt.eval_end) }}</div>
                            <div class="flex items-center">
                                <div class="flex h-1 bg-gray-200 rounded w-1/4 mr-3">
                                    <!-- <div class="h-1 bg-orange-300 rounded" :style="{ width: opt.progress + '%' }"></div> -->
                                    <div class="h-1 bg-orange-300 rounded" :style="{ width: 20 + '%' }"></div>
                                </div>
                                <!-- <div class="flex text-xs text-gray-600">Step 2 of step 7 completed</div> -->
                            </div>
                            
                                <div class="flex justify-start items-center space-x-2">
                                    <button @click="resumeOptimizer(opt.id)"
                                        class="p-1 m-1 text-xs flex items-center bg-orange-100 text-orange-700 hover:text-orange-900 hover:bg-orange-100 rounded transition-colors duration-200"
                                        title="Resume optimizer">
                                        Resume
                                    </button>
                                </div>
                        </div>

                        <!-- Pagination for Ongoing -->
                        <Pagination 
                            :current-page="ongoingOptimizers.currentPage"
                            :total-pages="ongoingOptimizers.totalPages"
                            :total-items="ongoingOptimizers.totalCount"
                            :page-size="ongoingOptimizers.pageSize"
                            @page-changed="onOngoingPageChange"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import Sidebar from './Sidebar.vue'
import Pagination from './common/Pagination.vue'
import { Activity, CheckCircle, Clock, Play } from 'lucide-vue-next';
import { useRouter } from 'vue-router'
import axios from 'axios';

const router = useRouter()
const search = ref('');
const loading = ref(false);
const isExpanded = ref(false)

// Pagination state for completed optimizers
const completedOptimizers = ref({
    data: [],
    currentPage: 1,
    totalPages: 0,
    totalCount: 0,
    pageSize: 10
});

// Pagination state for ongoing optimizers
const ongoingOptimizers = ref({
    data: [],
    currentPage: 1,
    totalPages: 0,
    totalCount: 0,
    pageSize: 10
});

const expandSidebar = () => {
  isExpanded.value = true;
};

const collapseSidebar = () => {
  isExpanded.value = false;
};

const createOptimizer = () => {
    router.push({ name: 'BuildOptimiser', query: { status: 'create' } });
};

const resumeOptimizer = (id) => {
    // Navigate to the optimizer details page
    // scenario_id = localStorage.setItem('scenario_id', id)
    router.push({ name: 'BuildOptimiser', query: { id: id, status: 'update' } });
};

function formatDate(isoString) {
    if (!isoString) return '';
    
    const date = new Date(isoString);
    if (isNaN(date)) return ''; // invalid date check
    
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
}
// API call for fetching optimizers
const fetchOptimizers = async (status, page = 1) => {
    try {
        const response = await axios.post('/scenario/scenario_list/', {
            page: page,
            page_size: 10,
            status: status
        });

        return {
            data: response.data.results,
            totalCount: response.data.count,
            totalPages: response.data.total_pages,
            currentPage: page
        };
    } catch (error) {
        console.error(`Error fetching ${status} optimizers:`, error);
        return {
            data: [],
            totalCount: 0,
            totalPages: 0,
            currentPage: page
        };
    }
};

// Load completed optimizers
const loadCompletedOptimizers = async (page = 1) => {
    loading.value = true;
    try {
        const result = await fetchOptimizers('COMPLETED', page);
        completedOptimizers.value = {
            ...result,
            pageSize: 10
        };
    } finally {
        loading.value = false;
    }
};

// Load ongoing optimizers
const loadOngoingOptimizers = async (page = 1) => {
    loading.value = true;
    try {
        const result = await fetchOptimizers('CREATED', page);
        ongoingOptimizers.value = {
            ...result,
            pageSize: 10
        };
    } finally {
        loading.value = false;
    }
};

// Load both optimizer types
const loadAllOptimizers = async () => {
    loading.value = true;
    try {
        await Promise.all([
            loadCompletedOptimizers(completedOptimizers.value.currentPage),
            loadOngoingOptimizers(ongoingOptimizers.value.currentPage)
        ]);
    } finally {
        loading.value = false;
    }
};

// Simplified pagination handlers using the common component
const onCompletedPageChange = async (page) => {
    await loadCompletedOptimizers(page);
};

const onOngoingPageChange = async (page) => {
    await loadOngoingOptimizers(page);
};

// Search handler (you can implement search API call if needed)
const handleSearch = () => {
    // If you have search API endpoint, you can implement it here
    // For now, it will just filter the current data
    console.log('Searching for:', search.value);
};


// Load data on component mount
onMounted(() => {
    loadAllOptimizers();
});
</script>

<style scoped>
input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.5);
}
</style>