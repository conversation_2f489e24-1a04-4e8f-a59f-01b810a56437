<template>
  <div>
  <!-- <div class="bg-white rounded-2xl shadow-sm border border-gray-200"> -->
  <div >
    <div class="px-4 py-6 sm:px-6 sm:py-8 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-3">
          <label class="flex items-center text-sm font-medium mb-2">
            <Space class="w-4 h-4 mr-2 text-secondary" />Scenario Name<span class="text-red-500 ml-1">*</span>
          </label>
          <input type="text" v-model="formData.scenarioName" placeholder="Enter Scenario Name"
            class="w-full bg-white p-3 h-10 border border-primary text-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" />
        </div>
        
        
        <div class="space-y-5">
          <label class="flex items-center text-sm font-semibold">
            <Bolt class="w-4 h-4 mr-2 text-secondary" />
            Territory <span class="text-red-500 ml-1">*</span>
          </label>
          <DynamicFilter
          v-model="formData.territory"
          :multiselect="false"
          label="Territories"
          placeholder="Select Territories"
          :options="territories"
           value-key="code"
           label-key="name"
          :searchable="false"
          variant="outline"
          size="sm"
        />
        
        </div>
        <!-- Store Configuration -->
        <div class="space-y-3">
          <label class="flex items-center text-sm font-semibold">
            <Bolt class="w-4 h-4 mr-2 text-secondary" />
            Store Configuration <span class="text-red-500 ml-1">*</span>
          </label>
          <DynamicFilter
          v-model="formData.configurationStore"
          :multiselect="false"
          label="Store Configuration"
          placeholder="Select Store Configuration"
          :options="configurationStoreOptions"
          :searchable="false"
          variant="outline"
          size="sm"
          @update:modelValue="onConfigurationSelect"
        />
          
        </div>

        <div class="space-y-3">
          <label class="flex items-center text-sm font-semibold">
            <SunSnow class="w-4 h-4 mr-2 text-secondary" />
            Season Type <span class="text-red-500 ml-1">*</span>
          </label>
          <DynamicFilter
          v-model="formData.seasonType"
          :multiselect="false"
          label="Season Type"
          placeholder="Select Season Type"
          :options="seasonTypeOptions"
           value-key="value"
           label-key="label"
          :searchable="false"
          variant="outline"
          size="sm"
          @update:modelValue="onSeasonTypeSelect"
        />
      </div> 
         
        
        <!-- Performance Metric -->
        <div class="space-y-3">
          <label class="flex items-center text-sm font-semibold">
            <Target class="w-4 h-4 mr-2 text-secondary" />
            Performance Metric <span class="text-red-500 ml-1">*</span>
          </label>
          <DynamicFilter
          v-model="formData.performanceMetric"
          :multiselect="false"
          label="Performance metric"
          placeholder="Select performance metric"
          :options="performanceOptions"
           value-key="value"
           label-key="label"
          :searchable="false"
          variant="outline"
          size="sm"
        />
          
        </div>

        <div class="space-y-3">
          <label class="flex items-center text-sm font-semibold">
            <SunSnow class="w-4 h-4 mr-2 text-secondary" />
            Event <span class="text-red-500 ml-1">*</span>
          </label>
          <input type="text" v-model="formData.event" placeholder="Enter Event Name"
            class="w-full bg-white p-3 h-10 border border-primary rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary" />
        </div>
      </div>

      <!-- Store Selection -->
      <div v-if="formData.configurationStore === 'Selected Stores'" class="mt-6 w-1/2">
        <div class="space-y-3">
          <label class="flex items-center text-sm font-semibold">
            <Filter class="w-4 h-4 mr-2 text-secondary" />
            Store Selection <span class="text-red-500 ml-1">*</span>
          </label>
          <DynamicFilter
          v-model="formData.storeSelection"
          :multiselect="true"
          label="stores"
          placeholder="Select stores"
          :options="storeOptions"
           value-key="value"
           label-key="label"
          :searchable="false"
          variant="outline"
          size="sm"
         
        />
          
        </div>
      </div>

      <!-- Evaluation Period -->
      <div class="mt-8 text-sm">
        <h3 class="text-md font-semibold mb-4 flex items-center">
          <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
            <Calendar class="w-4 h-4 text-secondary" />
          </div>
          Evaluation Period <span class="text-red-500 ml-1">*</span>
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium mb-2">Start Date</label>
            <input type="date" v-model="formData.evaluationPeriod.startDate"
              class="w-full px-4 py-3 bg-white h-10 border border-primary rounded-lg focus:ring-2 focus:ring-secondary" />
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">End Date</label>
            <input type="date" v-model="formData.evaluationPeriod.endDate"
              class="w-full px-4 py-3 bg-white border h-10 border-primary rounded-lg focus:ring-2 focus:ring-secondary" />
          </div>
        </div>
      </div>
      
      <!-- Reference Period -->
      <div class="mt-8 text-sm">
        <h3 class="text-md font-semibold mb-4 flex items-center">
          <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
            <Calendar class="w-4 h-4 text-secondary" />
          </div>
          Reference Period <span class="text-red-500 ml-1">*</span>
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium mb-2">Start Month</label>
            <input type="month" v-model="formData.referencePeriod.startDate"
              class="w-full px-4 py-3 bg-white h-10 text-sm border border-green-100 rounded-lg focus:ring-2 focus:ring-secondary" />
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">End Month</label>
            <input type="month" v-model="formData.referencePeriod.endDate"
              class="w-full px-4 py-3 bg-white border text-sm h-10 border-green-100 rounded-lg focus:ring-2 focus:ring-secondary" />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

  <!-- File Upload Section -->
  <div class="p-10">
    <div class="flex items-center mb-4">
      <div class="w-8 h-8 bg-primary rounded-xl flex items-center justify-center mr-4">
        <Upload class="w-4 h-4 text-secondary" />
      </div>
      <div>
        <h2 class="text-md font-bold">Upload Files</h2>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <FileUploadArea fileKey="sqft" label="SQFT file <span class='text-red-500 ml-1'>*</span>" :icon="BarChart3" />
      <FileUploadArea fileKey="mdq" label="MDQ/ROS" :icon="TrendingUp" />
      <FileUploadArea fileKey="cover" label="Subclass Cover <span class='text-red-500 ml-1'>*</span>" :icon="FileText" />
      <FileUploadArea fileKey="exclusion" label="Exclusions" :icon="SquaresExclude" />
    </div>

    <!-- Continue Button -->
    <div class="flex justify-end mt-8">
      <button 
        @click="handleContinue"
        :disabled="!isFormComplete || isLoading"
        :class="{
          'bg-green-600 hover:bg-green-700 text-white cursor-pointer': isFormComplete && !isLoading,
          'bg-gray-300 text-gray-500 cursor-not-allowed': !isFormComplete || isLoading
        }"
        class="px-8 py-3 rounded-lg font-semibold text-sm transition-colors duration-200 flex items-center"
      >
        <div v-if="isLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
        Continue
      </button>
    </div>
  </div>

</template>




<script setup>

import { onMounted, ref, reactive, provide, toRaw, watch, computed } from 'vue'
import Multiselect from 'vue-multiselect'
import DynamicFilter from './common/DynamicFilter.vue'
import Datepicker from 'vue3-datepicker'
import FileUploadArea from './FileUploadArea.vue'
import { Calendar, Filter, ChevronDown, Space, Target, Check, X, Upload, BarChart3, TrendingUp, FileText, SquaresExclude, Bolt, SunSnow } from 'lucide-vue-next'
import { fetchStores, saveScenarioAPI } from '../services/api'
import { useStepsStore } from '../stores/NavigationStore'
import { useScenarioStore } from '../stores/ScenarioStore'
import axios from 'axios'
import { useRoute } from 'vue-router'
const route = useRoute();
const stepsStore = useStepsStore()
const scenarioStore = useScenarioStore()

// Add loading state for continue button
const isLoading = ref(false)

const uploadedFiles = ref({
  sqftFile: null,
  mdqRos: null,
  subclassCover: null,
  exclusions: null
})

const dragActive = ref(null)
const loadingStores = ref(false)

// Computed property to check if form is complete
const isFormComplete = computed(() => {
  const requiredFields = [
    formData.scenarioName,
    formData.territory,
    formData.configurationStore,
    formData.seasonType,
    formData.performanceMetric,
    formData.event,
    formData.evaluationPeriod.startDate,
    formData.evaluationPeriod.endDate,
    formData.referencePeriod.startDate,
    formData.referencePeriod.endDate,
    uploadedFiles.value.sqft, // require SQFT file
    uploadedFiles.value.cover // require Cover file
  ]

  // Check if Selected Stores configuration requires store selection
  if (formData.configurationStore === 'Selected Stores' && (!formData.storeSelection || formData.storeSelection.length === 0)) {
    return false
  }

  // Check all required fields are filled
  const allFieldsFilled = requiredFields.every(field => field !== null && field !== '' && field !== undefined)

  // Check all required files are uploaded
  // const allFilesUploaded = Object.values(uploadedFiles.value).every(file => file !== null)

  return allFieldsFilled 
})

const handleContinue = async () => {
  if (isFormComplete.value && !isLoading.value) {
    isLoading.value = true
    const user_id = sessionStorage.getItem('user_id')
    const concpt = localStorage.getItem('concept') || 'hb'; // default to hb for now
    
    // Prepare payload object, map all fields you want to send
    const payload = {
      name: formData.scenarioName,
      season_type: formData.seasonType,
      user_id: user_id,  // or get dynamically
      eval_type: formData.configurationStore,
      event_name: formData.event,
      eval_start: formData.evaluationPeriod.startDate,
      eval_end: formData.evaluationPeriod.endDate,
      ref_start: formData.referencePeriod.startDate,
      ref_end: formData.referencePeriod.endDate,
      loc_cd: JSON.stringify(formData.storeSelection), // or array depending on backend
      CNCPT_NM: "hb",//update cncpt_nm to use concept from localstorage
      TERRITORY_NM: formData.territory?.code || formData.territory, // use code or name accordingly
      metric: formData.performanceMetric,
      sqft_file_id: uploadedFiles.value.sqftFile || null,
      mdq_file_id: uploadedFiles.value.mdqRos || null,
      cover_file_id: uploadedFiles.value.subclassCover || null,
      exclusion_file_id: uploadedFiles.value.exclusions || null,
      created_by: user_id,
      updated_by: user_id,
    }
    if (route.query.id) {
      payload.id = route.query.id;
    }
    
    try {
      // Call your API method here
      const response = await saveScenarioAPI(payload)
      console.log("Scenario saved:", response)

      if (response && response.scenario_details) {
        scenarioStore.setCurrentScenario(response.scenario_details)
      }

      const locCodes = await axios.post('/scenario/select_store_dropdown/', {
           "concept":"hb",
           "territory":formData.territory?.code || formData.territory
        });
      if(locCodes.data.length > 0){
        const loc_codes_all = locCodes.data.map(item => item.value);
        sessionStorage.setItem('loc_codes_all', JSON.stringify(loc_codes_all));
      }  

      sessionStorage.setItem('loc_codes', JSON.stringify(formData.storeSelection || []));
      sessionStorage.setItem('territory_name', formData.territory?.code || formData.territory || '');
      stepsStore.goToNextStep()
    } catch (error) {
      console.error("Failed to save scenario:", error)
      scenarioStore.setError(error.message || 'Failed to save scenario')
    } finally {
      isLoading.value = false
    }
  }
}

const handleFileUpload = (key, file) => {
  uploadedFiles.value[key] = file
}

const fetchStoresByTerritory = async (territoryCode) => {
  
  loadingStores.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    const response = await axios.post('/scenario/select_store_dropdown/', {
           "concept":"hb",
           "territory":territoryCode
        });

        return response.data

  } catch (error) {
    console.error('Error fetching stores:', error)
    return []
  } finally {
    loadingStores.value = false
  }
}

const onTerritorySelect = async (territory) => {
  if (territory && formData.configurationStore === 'Selected Stores') {
    const stores = await fetchStoresByTerritory(territory.code)
    storeOptions.value = stores
    // Clear previous selections when territory changes
    formData.storeSelection = []
  }
}

const onConfigurationSelect = async (value) => {
  console.log("value--->",value);
  
  if (value === 'Selected Stores') {
    // stepsStore.setStepVisible('Clusters', false)
    stepsStore.setStepVisible('Test & Control', true)
    
    // Fetch stores if territory is already selected
    if (formData.territory) {
      const stores = await fetchStoresByTerritory(formData.territory)
      storeOptions.value = stores
    }
  }
  if (value === 'Test & Control') {
    stepsStore.setStepVisible('Test & Control', false)
    // stepsStore.setStepVisible('Clusters', true)
    // Clear store options when switching away from Selected Stores
    storeOptions.value = []
    formData.storeSelection = []
  }
  if (value === null) {
    stepsStore.setStepVisible('Test & Control', true)
    stepsStore.setStepVisible('Clusters', true)
    // Clear store options when switching away from Selected Stores
    storeOptions.value = []
    formData.storeSelection = []
  }
}

const onSeasonTypeSelect = (val) => {
  
  if (val === 'in season') {
    // stepsStore.setStepVisible('Range', false)
    stepsStore.setStepVisible('Optimize', false)
  }
  if (val === 'pre season') {
    stepsStore.setStepVisible('Range', true)
    stepsStore.setStepVisible('Optimize', false)
  }
  if (val === null) {
    stepsStore.setStepVisible('Range', true)
    stepsStore.setStepVisible('Optimize', true)
  }
}

const setDragActive = (key) => {
  dragActive.value = key
}

provide('uploadedFiles', uploadedFiles)
provide('dragActive', dragActive)
provide('handleFileUpload', handleFileUpload)
provide('setDragActive', setDragActive)

const formData = reactive({
  scenario_id: null,
  configurationStore: null,
  territory: null,
  scenarioName: '',
  seasonPreference: 'in-season',
  seasonType: null,
  performanceMetric: null,
  storeSelection: [],
  event: null,
  evaluationPeriod: {
    startDate: '',
    endDate: ''
  },
  referencePeriod: {
    startDate: '',
    endDate: ''
  },
  territory: ''
})


const territory = [
  { code: 'AE', label: 'UAE' },
  { code: 'BH', label: 'Bahrain' },
  { code: 'EG', label: 'Egypt' },
  { code: 'KS', label: 'KSA' },
  { code: 'KW', label: 'Kuwait' },
  { code: 'OM', label: 'Oman' },
  { code: 'QT', label: 'Qatar' },
  { code: 'LB', label: 'Lebanon' },
  { code: 'JD', label: 'Jordan' }
]



const fillFormFromStore = (storedData) => {
  if (storedData && Object.keys(storedData).length > 0) {
    formData.scenario_id = storedData.id ?? formData.scenario_id
    formData.configurationStore = storedData.eval_type ?? formData.configurationStore
    formData.territory = storedData.TERRITORY_NM ?? formData.territory
    formData.scenarioName = storedData.name ?? formData.scenarioName
    formData.seasonType = storedData.season_type ?? formData.seasonType
    formData.performanceMetric = storedData.metric ?? formData.performanceMetric
    formData.storeSelection = storedData.loc_cd ? JSON.parse(storedData.loc_cd) : formData.storeSelection
    formData.event = storedData.event_name ?? formData.event
    formData.evaluationPeriod.startDate = formatDate(storedData.eval_start) || formData.evaluationPeriod.startDate
    formData.evaluationPeriod.endDate = formatDate(storedData.eval_end) || formData.evaluationPeriod.endDate
    formData.referencePeriod.startDate = formatMonth(storedData.ref_start) || formData.referencePeriod.startDate
    formData.referencePeriod.endDate = formatMonth(storedData.ref_end) || formData.referencePeriod.endDate
  }
}

onMounted(() => {
  fillFormFromStore(stepsStore.getScenarioData)
})

watch(() => stepsStore.getScenarioData, (newData) => {
  fillFormFromStore(newData)
}, { deep: true, immediate: false })

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  if (isNaN(date)) return '' // handle invalid date
  return date.toISOString().split('T')[0] // "YYYY-MM-DD"
}
const formatMonth = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  if (isNaN(date)) return ''
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}` // "YYYY-MM"
}

const storeDropdownOpen = ref(false)
const storeOptions = ref([])
const configurationStoreOptions = ['Test & Control', 'Selected Stores']
const territories = [
  { code: "AE", name: "UAE" },
  { code: "BH", name: "Bahrain" },
  { code: "EG", name: "Egypt" },
  { code: "KS", name: "KSA" },
  { code: "KW", name: "Kuwait" },
  { code: "OM", name: "Oman" },
  { code: "QT", name: "Qatar" },
  { code: "LB", name: "Lebanon" },
  { code: "JD", name: "Jordan" }
]
const seasonTypeOptions = [
  { value: 'in season', label: 'In Season' },
  { value: 'pre season', label: 'Pre Season' }
]
const performanceOptions = [
  { value: 'revenue', label: 'Revenue' },
  { value: 'margin', label: 'Margin' }
]
const eventOptions = [
  { value: 'Eid', label: 'Eid' },
  { value: 'Ramadan', label: 'Ramadan' },
  { value: 'Back to School', label: 'Back to School' },
  { value: 'Summer Sale', label: 'Summer Sale' }
]

const toggleStoreSelection = (store) => {
  const storeCode = store.code; // extract the code only
  const index = formData.storeSelection.indexOf(storeCode);
  if (index === -1) {
    formData.storeSelection.push(storeCode);  // push only the code
  } else {
    formData.storeSelection.splice(index, 1); // remove by code
  }
};

// const getTerritoryName = (code) => {
//   const found = locations.find(loc => loc.code === code)
//   return found ? found.label : 'Unknown'
// }

const onTerritoryChange = async (selectedTerritory) => {
  console.log('Territory selected:', selectedTerritory)
  const concept = localStorage.getItem('concept')
  // Example: Fetch store list based on selected territory and concept
  if (selectedTerritory) {
    const stores = await fetchStores(concept, selectedTerritory.code)
    console.log("stores value is ", stores)
    storeOptions.value = stores.map(store => ({
      label: store.loc_nm,
      code: store.loc_cd
    }))
  }
}




const generateAnalysis = () => {
  console.log('Generating analysis with:', JSON.stringify(formData, null, 2))
  // You can replace this with actual logic

}


const removeStore = (store) => {
  formData.storeSelection = formData.storeSelection.filter(s => s !== store)
}
</script>

<style scoped>
.rotate-180 {
  transform: rotate(180deg);
}
</style>