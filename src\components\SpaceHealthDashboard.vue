<script setup lang="ts">
import { ref } from 'vue'
import FilterBarTest from './FilterBarTest.vue'
import SpaceHealthMetricsTable from './SpaceHealthMetricsTable.vue'

const filteredMetricsData = ref([])
const loadingMetrics = ref(false)
const errorMetrics = ref<string | null>(null)

// Handler for filter updates from child
const mapApiRowToTableRow = (item) => ({
  group: item.grp_nm,
  department: item.dpt_nm,
  class: item.clss_nm,
  subClass: item.sub_clss_nm,
  lm: item.lm,
  lmCont: item.lm_contribution_in_store,
  optionCount: item.option_count,
  optionDensity: item.option_density,
  sohQty: item.soh,
  stockDensity: item.stock_density,
  revenue: item.rev,
  gmv: item.gmv,
  revenuePerDay: item.rev_per_day,
  gmvPerDay: item.gmv_per_day,
  gmvPerLmPerDay: item.gmv_per_lm_per_day,
  revPerLmPerDay: item.rev_per_lm_per_day,
  lmRank: item.lm_rank,
  revPerLmPerDayRank: item.rev_per_lm_per_day_rank,
  gmvPerLmPerDayRank: item.gmv_per_lm_per_day_rank,
  // Add more if the table expects additional fields
})

const handleFilteredData = (payload) => {
  filteredMetricsData.value = Array.isArray(payload.data) ? payload.data.map(mapApiRowToTableRow) : [];
  loadingMetrics.value = payload.loading;
  errorMetrics.value = payload.error;
}
</script>

<template>
  <div class="flex flex-col h-full">
    <!-- Filter Bar -->
    <div class="flex-shrink-0 bg-primary">
      <FilterBarTest @update:metrics="handleFilteredData" />
    </div>

    <!-- Table -->
    <div class="flex-1 overflow-auto p-4 sm:p-6 lg:p-8">
      <div class="rounded shadow p-4">
        <SpaceHealthMetricsTable
          :rows="filteredMetricsData"
          :loading="loadingMetrics"
          :error="errorMetrics"
        />
      </div>
    </div>
  </div>
</template>