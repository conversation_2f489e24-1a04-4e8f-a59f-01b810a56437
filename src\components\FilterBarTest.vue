<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import DynamicFilter from './common/DynamicFilter.vue'
import axios from 'axios'

// Props/Emits
const emit = defineEmits(['update:metrics'])

// Filter state
const selectedStores = ref('')
const selectedGroup = ref<string[]>([])
const selectedDepartment = ref<string[]>([])
const selectedClasses = ref<string[]>([])
const selectedSubClass = ref<string[]>([])

// API state
const filteredMetricsData = ref([])
const loadingMetrics = ref(false)
const errorMetrics = ref<string | null>(null)

// Applied filters snapshot
const appliedFilters = ref({
  stores: '',
  group: [],
  department: [],
  class: [],
  subClass: []
})

// Clear all filters
const clearAllFilters = () => {
  selectedGroup.value = []
  selectedDepartment.value = []
  selectedClasses.value = []
  selectedSubClass.value = []
  // Reload all data
  loadAllMetricsData()
}

// Apply filters
const applyFilters = async () => {
  appliedFilters.value = {
    stores: selectedStores.value,
    group: selectedGroup.value,
    department: selectedDepartment.value,
    class: selectedClasses.value,
    sub_class: selectedSubClass.value
  }

  await fetchMetrics('scenario/health-metrics/', appliedFilters.value)
}

// Load all metrics (default)
const loadAllMetricsData = async () => {
  await fetchMetrics('scenario/health-metrics/',{})
}

// Shared fetch logic
const fetchMetrics = async (url, payload) => {
  loadingMetrics.value = true
  errorMetrics.value = null

  try {
    const response = await axios.get(url, { params: payload })

    filteredMetricsData.value = response.data.results || response.data
  } catch (err: any) {
    errorMetrics.value = err.message || 'Failed to load metrics data.'
    filteredMetricsData.value = []
  } finally {
    loadingMetrics.value = false
    // Always emit updated data to parent
    emit('update:metrics', {
      data: filteredMetricsData.value,
      loading: loadingMetrics.value,
      error: errorMetrics.value
    })
  }
}

// GDCS store data
interface GdcsDataItem {
  loc_cd: string
  loc_nm: string
  grp_nm: string
  dpt_nm: string
  clss_nm: string
  sub_clss_nm: string
}

const scenarioDetails = {
  concept: 'hb',
  scenario_id: 1,
  gdcsData: ref<GdcsDataItem[]>([])
}

const getGdcsData = async () => {
  try {
    const response = await axios.post('scenario/getAllGDCSdata/', {
      concept: scenarioDetails.concept,
      scenario_id: scenarioDetails.scenario_id
    })
    scenarioDetails.gdcsData.value = response.data.gdcs_data
    if (scenarioDetails.gdcsData.value.length > 0) {
      selectedStores.value = scenarioDetails.gdcsData.value[0].loc_cd
    }
  } catch (err) {
    console.error('Error fetching GDCS Data:', err)
  }
}

// Computed filter options
const uniqueStores = computed(() => {
  const stores = scenarioDetails.gdcsData.value.map(item => ({
    value: item.loc_cd,
    label: `${item.loc_nm} (${item.loc_cd})`
  }))
  return [...new Map(stores.map(item => [item.value, item])).values()]
})

const uniqueGroups = computed(() => {
  let filtered = scenarioDetails.gdcsData.value
  if (selectedStores.value) {
    filtered = filtered.filter(item => item.loc_cd === selectedStores.value)
  }
  const groups = [...new Set(filtered.map(item => item.grp_nm))]
  return groups.map(group => ({ value: group, label: group }))
})

const uniqueDepartments = computed(() => {
  let filtered = scenarioDetails.gdcsData.value
  if (selectedStores.value) {
    filtered = filtered.filter(item => item.loc_cd === selectedStores.value)
  }
  if (selectedGroup.value.length > 0) {
    filtered = filtered.filter(item => selectedGroup.value.includes(item.grp_nm))
  }
  const departments = [...new Set(filtered.map(item => item.dpt_nm))]
  return departments.map(dept => ({ value: dept, label: dept }))
})

const uniqueClasses = computed(() => {
  let filtered = scenarioDetails.gdcsData.value
  if (selectedStores.value) {
    filtered = filtered.filter(item => item.loc_cd === selectedStores.value)
  }
  if (selectedGroup.value.length > 0) {
    filtered = filtered.filter(item => selectedGroup.value.includes(item.grp_nm))
  }
  if (selectedDepartment.value.length > 0) {
    filtered = filtered.filter(item => selectedDepartment.value.includes(item.dpt_nm))
  }
  const classes = [...new Set(filtered.map(item => item.clss_nm))]
  return classes.map(cls => ({ value: cls, label: cls }))
})

const uniqueSubClasses = computed(() => {
  let filtered = scenarioDetails.gdcsData.value
  if (selectedStores.value) {
    filtered = filtered.filter(item => item.loc_cd === selectedStores.value)
  }
  if (selectedGroup.value.length > 0) {
    filtered = filtered.filter(item => selectedGroup.value.includes(item.grp_nm))
  }
  if (selectedDepartment.value.length > 0) {
    filtered = filtered.filter(item => selectedDepartment.value.includes(item.dpt_nm))
  }
  if (selectedClasses.value.length > 0) {
    filtered = filtered.filter(item => selectedClasses.value.includes(item.clss_nm))
  }
  const subClasses = [...new Set(filtered.map(item => item.sub_clss_nm))]
  return subClasses.map(subCls => ({ value: subCls, label: subCls }))
})

// On mount: load GDCS + all metrics
onMounted(async () => {
  await getGdcsData()
  await loadAllMetricsData()
})
</script>

<template>
  <div class="px-6 pt-6 w-full mx-auto">
    <div class="flex w-full flex-wrap gap-3 items-end">
      <!-- Groups -->
      <div class="space-y-1 flex-1 min-w-[12%]">
        <label class="block text-xs font-medium text-gray-600">Groups</label>
        <DynamicFilter v-model="selectedGroup" :multiselect="true" label="Groups"
          placeholder="Select Groups" :options="uniqueGroups" variant="outline" size="sm" />
      </div>

      <!-- Department -->
      <div class="space-y-1 flex-1 min-w-[12%]">
        <label class="block text-xs font-medium text-gray-600">Department</label>
        <DynamicFilter v-model="selectedDepartment" :multiselect="false" label="Department"
          placeholder="Select Department" :options="uniqueDepartments" variant="outline" size="sm" />
      </div>

      <!-- Class -->
      <div class="space-y-1 flex-1 min-w-[12%]">
        <label class="block text-xs font-medium text-gray-600">Class</label>
        <DynamicFilter v-model="selectedClasses" :multiselect="true" label="Class"
          placeholder="Select Class" :options="uniqueClasses" variant="outline" size="sm" />
      </div>

      <!-- Sub Class -->
      <div class="space-y-1 flex-1 min-w-[12%]">
        <label class="block text-xs font-medium text-gray-600">Sub Class</label>
        <DynamicFilter v-model="selectedSubClass" :multiselect="true" label="Sub Class"
          placeholder="Select Sub Class" :options="uniqueSubClasses" variant="outline" size="sm" />
      </div>

      <!-- Apply Button -->
      <div class="space-y-1 flex-1 min-w-[12%]">
        <button
          class="flex items-center gap-2 px-3 py-2 text-xs bg-tertiary text-white rounded hover:bg-green-900 transition-colors"
          @click="applyFilters">
          Apply Filters
        </button>
      </div>

      <!-- Clear Button -->
      <div class="items-center flex-1 min-w-[12%]">
        <button
          class="px-4 py-2 text-xs font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100"
          @click="clearAllFilters">
          Clear All
        </button>
      </div>
    </div>
  </div>
</template>
